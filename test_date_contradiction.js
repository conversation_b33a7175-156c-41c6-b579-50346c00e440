// Test script for the fixed date/version contradiction detection
const apiService = require('./src/services/apiService');

// Test the specific Q12 issue that was incorrectly "corrected"
const testQuestions = [
  {
    question: "The Backstage view in Word 2010 was introduced in Word 2007.",
    answer: false, // Should STAY FALSE (correct)
    explanation: "The text states that the Backstage view was introduced in Word 2010."
  },
  {
    question: "The default margins in Word 2010 are set to 2 inches on all sides.",
    answer: false, // Should STAY FALSE (correct)
    explanation: "The text states that the default margins are set to 1 inch, not 2 inches."
  },
  {
    question: "Microsoft Word is primarily used for creating spreadsheets.",
    answer: true, // Should be CORRECTED to FALSE (wrong)
    explanation: "Microsoft Word is for word processing, not spreadsheet creation. Spreadsheets are typically created using software like Microsoft Excel."
  },
  {
    question: "The File tab provides access to the Backstage view for file-related operations.",
    answer: true, // Should STAY TRUE (correct)
    explanation: "The text confirms that the File tab leads to the Backstage view for managing files."
  }
];

console.log('🔍 Testing Fixed Date/Version Contradiction Detection');
console.log('==================================================');

console.log('\n📋 Test Questions:');
testQuestions.forEach((q, index) => {
  console.log(`${index + 1}. ${q.question.substring(0, 70)}...`);
  console.log(`   Answer: ${q.answer}`);
  console.log(`   Expected: ${index === 0 || index === 1 ? 'Stay False' : index === 2 ? 'Change to False' : 'Stay True'}`);
  console.log('');
});

console.log('🔧 Running Fixed Logic Validation...');

// Test each question individually
const results = [];
testQuestions.forEach((q, index) => {
  console.log(`\n🔍 Testing Question ${index + 1}:`);
  const result = apiService.validateTrueFalseLogic({ ...q }, index + 1);
  const wasChanged = result.answer !== q.answer;
  
  results.push(result);
  console.log(`   Original: ${q.answer} → Result: ${result.answer}`);
  console.log(`   Changed: ${wasChanged ? 'YES' : 'NO'}`);
  
  // Check if the change was correct
  let expectedAnswer;
  if (index === 0 || index === 1) {
    expectedAnswer = false; // Q1-Q2 should stay FALSE
  } else if (index === 2) {
    expectedAnswer = false; // Q3 should be FALSE
  } else {
    expectedAnswer = true; // Q4 should stay TRUE
  }
  
  const isCorrect = result.answer === expectedAnswer;
  console.log(`   Expected: ${expectedAnswer}, Got: ${result.answer} ${isCorrect ? '✅' : '❌'}`);
});

console.log('\n📊 Fixed Validation Results:');
const corrections = results.filter((r, i) => r.answer !== testQuestions[i].answer).length;

console.log(`   - Total questions tested: ${testQuestions.length}`);
console.log(`   - Questions changed: ${corrections}/${testQuestions.length}`);

console.log('\n🔍 Individual Results:');
results.forEach((r, i) => {
  const originalAnswer = testQuestions[i].answer;
  let expectedAnswer;
  
  if (i === 0 || i === 1) {
    expectedAnswer = false; // Q1-Q2 should be FALSE
  } else if (i === 2) {
    expectedAnswer = false; // Q3 should be FALSE
  } else {
    expectedAnswer = true; // Q4 should be TRUE
  }
  
  const isCorrect = r.answer === expectedAnswer;
  console.log(`   Q${i + 1}: ${isCorrect ? 'CORRECT ✅' : 'INCORRECT ❌'} (${originalAnswer} → ${r.answer})`);
});

const allCorrect = results.every((r, i) => {
  if (i === 0 || i === 1) return r.answer === false; // Q1-Q2 should be FALSE
  if (i === 2) return r.answer === false; // Q3 should be FALSE
  return r.answer === true; // Q4 should be TRUE
});

console.log('\n🎯 System Verification:');
console.log(`   - Date contradiction detection: ${results[0].answer === false ? 'WORKING ✅' : 'FAILED ❌'}`);
console.log(`   - Number contradiction detection: ${results[1].answer === false ? 'WORKING ✅' : 'FAILED ❌'}`);
console.log(`   - Real contradictions caught: ${results[2].answer === false ? 'YES ✅' : 'NO ❌'}`);
console.log(`   - Correct statements preserved: ${results[3].answer === true ? 'YES ✅' : 'NO ❌'}`);
console.log(`   - Overall accuracy: ${allCorrect ? 'PERFECT ✅' : 'NEEDS WORK ❌'}`);

if (allCorrect) {
  console.log('\n🎉 SUCCESS: Date/version contradiction detection working perfectly!');
  console.log('✅ Q12-type issues (Word 2007 vs Word 2010) now correctly handled');
  console.log('✅ Number contradictions (2 inches vs 1 inch) properly detected');
  console.log('✅ Real logic contradictions still detected and corrected');
  console.log('✅ Educational accuracy maintained across all question types');
} else {
  console.log('\n⚠️ ISSUE: Some validation problems remain.');
  console.log('Additional refinement may be needed.');
}

// Test the hasDateVersionContradiction function directly
console.log('\n🔍 Direct Function Testing:');
const testCases = [
  {
    question: "The Backstage view in Word 2010 was introduced in Word 2007.",
    explanation: "The text states that the Backstage view was introduced in Word 2010.",
    expected: true
  },
  {
    question: "The default margins are 2 inches.",
    explanation: "The text states that the default margins are 1 inch.",
    expected: true
  },
  {
    question: "Word 2010 is a word processor.",
    explanation: "The text confirms that Word 2010 is a word processing application.",
    expected: false
  }
];

testCases.forEach((test, index) => {
  const result = apiService.hasDateVersionContradiction(test.question, test.explanation);
  const isCorrect = result === test.expected;
  console.log(`   Test ${index + 1}: ${isCorrect ? 'PASS ✅' : 'FAIL ❌'} (Expected: ${test.expected}, Got: ${result})`);
});

console.log('\n✅ Date/version contradiction detection system is now robust and accurate!');
